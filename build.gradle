// Top-level build file where you can add configuration options common to all sub-projects/modules.
apply from: "config/config.gradle"

buildscript {
    ext.kotlin_version = '1.9.25'

    repositories {
        maven { url "https://maven.aliyun.com/repository/public" }
        maven { url "https://maven.aliyun.com/repository/google" }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        mavenCentral()
        maven {
            url 'https://maven.google.com/'
            name 'Google'
        }
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.9.2'
        classpath 'com.google.gms:google-services:4.3.10'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

allprojects {
    repositories {
        maven { url "https://maven.aliyun.com/repository/public" }
        maven { url "https://maven.aliyun.com/repository/google" }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        mavenCentral()
        maven {
            url "https://www.jitpack.io"
        }
        maven {
            url 'https://dl.google.com/dl/android/maven2/'
        }
        maven {
            url 'https://maven.google.com/'
            name 'Google'
        }
    }

    configurations.configureEach {
        resolutionStrategy {
            force 'org.xerial:sqlite-jdbc:3.34.0'
        }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
